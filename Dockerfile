# Multi-stage build para optimizar el tamaño de la imagen final
FROM node:20-alpine AS builder

# Instalar dependencias del sistema necesarias para compilar módulos nativos
RUN apk add --no-cache python3 make g++

# Establecer directorio de trabajo
WORKDIR /app

# Copiar archivos de configuración de dependencias
COPY package*.json ./

# Instalar todas las dependencias (incluyendo devDependencies para el build)
RUN npm ci

# Copiar código fuente y archivos de configuración
COPY . .

# Verificar que los archivos fuente estén presentes
RUN ls -la src/

# Compilar la aplicación TypeScript
RUN npm run build

# Verificar que la compilación fue exitosa
RUN ls -la dist/ && ls -la dist/

# Limpiar devDependencies después del build
RUN npm ci --only=production && npm cache clean --force

# Etapa de producción
FROM node:20-alpine AS production

# Crear usuario no-root para seguridad
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# Instalar dumb-init para manejo correcto de señales
RUN apk add --no-cache dumb-init

# Establecer directorio de trabajo
WORKDIR /app

# Cambiar propietario del directorio de trabajo
RUN chown -R nestjs:nodejs /app
USER nestjs

# Copiar node_modules desde la etapa de builder
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules

# Copiar aplicación compilada
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist

# Verificar que los archivos estén en su lugar
RUN ls -la dist/

# Copiar package.json para tener acceso a scripts si es necesario
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# Copiar script de entrada
COPY --chown=nestjs:nodejs docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh

# Exponer puerto (configurable via variable de entorno PORT)
EXPOSE 3000

# Variables de entorno por defecto
ENV NODE_ENV=production
ENV PORT=3000

# Healthcheck para verificar que la aplicación esté funcionando
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:${PORT}/api', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Usar dumb-init para manejo correcto de señales y ejecutar la aplicación
ENTRYPOINT ["dumb-init", "--", "./docker-entrypoint.sh"]
CMD ["node", "dist/src/main.js"]
