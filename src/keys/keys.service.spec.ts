import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException, BadRequestException } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { KeysService } from './keys.service';
import { UsersService } from '../users/users.service';
import { SeqrngClientService } from '../common/services/seqrng-client.service';
import { Key } from './entities/key.entity';

describe('KeysService', () => {
  let service: KeysService;
  let usersService: UsersService;
  let seqrngClientService: SeqrngClientService;

  const mockUsersService = {
    getUserCtmConfig: jest.fn(),
    findOne: jest.fn(),
  };

  const mockSeqrngClientService = {
    generateRandomBytes: jest.fn(),
    generateHexKey: jest.fn(),
    generateAlphanumericKey: jest.fn(),
    uploadKeyToCtm: jest.fn(),
    uploadBatchKeysToCtm: jest.fn(),
    checkKeyExists: jest.fn(),
    getCtmToken: jest.fn(),
  };

  const mockKeyRepository = {
    create: jest.fn().mockImplementation((data) => ({ ...data, id: 'mock-key-id' })),
    save: jest.fn().mockImplementation((entity) => Promise.resolve(entity)),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeysService,
        {
          provide: getRepositoryToken(Key),
          useValue: mockKeyRepository,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: SeqrngClientService,
          useValue: mockSeqrngClientService,
        },
      ],
    }).compile();

    service = module.get<KeysService>(KeysService);
    usersService = module.get<UsersService>(UsersService);
    seqrngClientService = module.get<SeqrngClientService>(SeqrngClientService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateRandomBytes', () => {
    it('should generate random bytes successfully', async () => {
      const userId = 'test-user-id';
      const generateBytesDto = { num_bytes: 32, packages: 1 };
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };
      const mockResponse = {
        status: 'success',
        message: 'Random bytes generated successfully',
        data: { random_bytes_base64: 'base64data' },
        timestamp: '2023-01-01T00:00:00Z',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockSeqrngClientService.generateRandomBytes.mockResolvedValue(mockResponse);

      const result = await service.generateRandomBytes(userId, generateBytesDto);

      expect(mockUsersService.getUserCtmConfig).toHaveBeenCalledWith(userId);
      expect(mockSeqrngClientService.generateRandomBytes).toHaveBeenCalledWith(
        generateBytesDto,
        mockCtmConfig,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw ForbiddenException when user has no CTM config', async () => {
      const userId = 'test-user-id';
      const generateBytesDto = { num_bytes: 32, packages: 1 };

      mockUsersService.getUserCtmConfig.mockResolvedValue(null);

      await expect(service.generateRandomBytes(userId, generateBytesDto)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw BadRequestException when SeQRNG API fails', async () => {
      const userId = 'test-user-id';
      const generateBytesDto = { num_bytes: 32, packages: 1 };
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockSeqrngClientService.generateRandomBytes.mockRejectedValue(
        new Error('SeQRNG API error'),
      );

      await expect(service.generateRandomBytes(userId, generateBytesDto)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('uploadKeyToCtm', () => {
    it('should upload key to CTM successfully', async () => {
      const userId = 'test-user-id';
      const uploadKeyDto = {
        key_name: 'test-key',
        algorithm: 'AES' as any,
        num_bytes: 32,
        exportable: false,
      };
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };
      const mockUser = {
        id: userId,
        firstName: 'John',
        lastName: 'Doe',
        company: 'Test Company',
        email: '<EMAIL>',
      };
      const mockResponse = {
        status: 'success',
        message: 'Key uploaded to CTM successfully',
        data: { key_name: 'test-key', upload_successful: true },
        timestamp: '2023-01-01T00:00:00Z',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockUsersService.findOne.mockResolvedValue(mockUser);
      mockSeqrngClientService.uploadKeyToCtm.mockResolvedValue(mockResponse);

      const result = await service.uploadKeyToCtm(userId, uploadKeyDto);

      expect(mockUsersService.getUserCtmConfig).toHaveBeenCalledWith(userId);
      expect(mockUsersService.findOne).toHaveBeenCalledWith(userId);
      expect(mockSeqrngClientService.uploadKeyToCtm).toHaveBeenCalledWith(
        { ...uploadKeyDto, owner: 'test_company' },
        mockCtmConfig,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should use firstName_lastName as owner when user has no company', async () => {
      const userId = 'test-user-id';
      const uploadKeyDto = {
        key_name: 'test-key',
        algorithm: 'AES' as any,
        num_bytes: 32,
        exportable: false,
      };
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };
      const mockUser = {
        id: userId,
        firstName: 'John',
        lastName: 'Doe',
        company: null, // No company
        email: '<EMAIL>',
      };
      const mockResponse = {
        status: 'success',
        message: 'Key uploaded to CTM successfully',
        data: { key_name: 'test-key', upload_successful: true },
        timestamp: '2023-01-01T00:00:00Z',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockUsersService.findOne.mockResolvedValue(mockUser);
      mockSeqrngClientService.uploadKeyToCtm.mockResolvedValue(mockResponse);

      const result = await service.uploadKeyToCtm(userId, uploadKeyDto);

      expect(mockUsersService.findOne).toHaveBeenCalledWith(userId);
      expect(mockSeqrngClientService.uploadKeyToCtm).toHaveBeenCalledWith(
        { ...uploadKeyDto, owner: 'john_doe' },
        mockCtmConfig,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should format company name with multiple spaces correctly', async () => {
      const userId = 'test-user-id';
      const uploadKeyDto = {
        key_name: 'test-key',
        algorithm: 'AES' as any,
        num_bytes: 32,
        exportable: false,
      };
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };
      const mockUser = {
        id: userId,
        firstName: 'John',
        lastName: 'Doe',
        company: 'Acme   Corporation   Inc', // Multiple spaces
        email: '<EMAIL>',
      };
      const mockResponse = {
        status: 'success',
        message: 'Key uploaded to CTM successfully',
        data: { key_name: 'test-key', upload_successful: true },
        timestamp: '2023-01-01T00:00:00Z',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockUsersService.findOne.mockResolvedValue(mockUser);
      mockSeqrngClientService.uploadKeyToCtm.mockResolvedValue(mockResponse);

      const result = await service.uploadKeyToCtm(userId, uploadKeyDto);

      expect(mockUsersService.findOne).toHaveBeenCalledWith(userId);
      expect(mockSeqrngClientService.uploadKeyToCtm).toHaveBeenCalledWith(
        { ...uploadKeyDto, owner: 'acme_corporation_inc' },
        mockCtmConfig,
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('checkKeyExists', () => {
    it('should check key existence successfully', async () => {
      const userId = 'test-user-id';
      const keyName = 'test-key';
      const mockCtmConfig = {
        ipAddress: 'https://ctm.example.com',
        username: 'ctm_user',
        password: 'ctm_pass',
        domain: 'root',
      };
      const mockResponse = {
        status: 'success',
        message: 'Key existence check completed',
        data: { key_name: 'test-key', exists: true },
        timestamp: '2023-01-01T00:00:00Z',
      };

      mockUsersService.getUserCtmConfig.mockResolvedValue(mockCtmConfig);
      mockSeqrngClientService.checkKeyExists.mockResolvedValue(mockResponse);

      const result = await service.checkKeyExists(userId, keyName);

      expect(mockUsersService.getUserCtmConfig).toHaveBeenCalledWith(userId);
      expect(mockSeqrngClientService.checkKeyExists).toHaveBeenCalledWith(
        keyName,
        mockCtmConfig,
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
