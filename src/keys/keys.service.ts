import { Injectable, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsersService } from '../users/users.service';
import { SeqrngClientService, CtmConfig, SeqrngConfig } from '../common/services/seqrng-client.service';
import { Key, KeyType, KeyStatus, KeyAlgorithm } from './entities/key.entity';
import { GenerateBytesDto } from './dto/generate-bytes.dto';
import { GenerateKeyDto } from './dto/generate-key.dto';
import { UploadKeyDto } from './dto/upload-key.dto';
import { UploadBatchKeysDto } from './dto/upload-batch-keys.dto';
import { GetKeysDto } from './dto/get-keys.dto';
import { KeyResponseDto, KeyListResponseDto } from './dto/key-response.dto';

@Injectable()
export class KeysService {
  private readonly logger = new Logger(KeysService.name);

  constructor(
    @InjectRepository(Key)
    private readonly keyRepository: Repository<Key>,
    private readonly usersService: UsersService,
    private readonly seqrngClientService: SeqrngClientService,
  ) {}

  /**
   * Map algorithm string to KeyAlgorithm enum
   */
  private mapAlgorithmToEnum(algorithm: string): KeyAlgorithm {
    const algorithmLower = algorithm.toLowerCase();

    switch (algorithmLower) {
      case 'aes':
        return KeyAlgorithm.AES;
      case 'aria':
        return KeyAlgorithm.ARIA;
      case 'rsa':
        return KeyAlgorithm.RSA;
      case 'hmac-sha1':
      case 'hmac_sha1':
        return KeyAlgorithm.HMAC_SHA1;
      case 'hmac-sha256':
      case 'hmac_sha256':
        return KeyAlgorithm.HMAC_SHA256;
      case 'hmac-sha384':
      case 'hmac_sha384':
        return KeyAlgorithm.HMAC_SHA384;
      case 'hmac-sha512':
      case 'hmac_sha512':
        return KeyAlgorithm.HMAC_SHA512;
      default:
        // Default to AES if unknown algorithm
        return KeyAlgorithm.AES;
    }
  }

  /**
   * Generate random bytes using user's CTM and SeqRNG configuration
   */
  async generateRandomBytes(userId: string, generateBytesDto: GenerateBytesDto) {
    this.logger.log(`Generating random bytes for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `random-bytes-${Date.now()}`,
      type: KeyType.RANDOM_BYTES,
      algorithm: null,
      numBytes: generateBytesDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null,
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      userId,
    });

    try {
      const result = await this.seqrngClientService.generateRandomBytes(
        generateBytesDto,
        ctmConfig,
        seqrngConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated ${generateBytesDto.num_bytes} random bytes for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate random bytes for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate random bytes: ${error.message}`);
    }
  }

  /**
   * Generate hexadecimal key using user's CTM and SeqRNG configuration
   */
  async generateHexKey(userId: string, generateKeyDto: GenerateKeyDto) {
    this.logger.log(`Generating hex key for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `hex-key-${Date.now()}`,
      type: KeyType.HEX_KEY,
      algorithm: null, // Will be set based on the generated key
      numBytes: generateKeyDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null, // No owner specified for generated keys
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      userId,
    });

    try {
      const result = await this.seqrngClientService.generateHexKey(
        generateKeyDto,
        ctmConfig,
        seqrngConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated hex key for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate hex key for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate hex key: ${error.message}`);
    }
  }

  /**
   * Generate alphanumeric key using user's CTM and SeqRNG configuration
   */
  async generateAlphanumericKey(userId: string, generateKeyDto: GenerateKeyDto) {
    this.logger.log(`Generating alphanumeric key for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: `alphanumeric-key-${Date.now()}`,
      type: KeyType.ALPHANUMERIC_KEY,
      algorithm: null, // Will be set based on the generated key
      numBytes: generateKeyDto.num_bytes,
      status: KeyStatus.GENERATED,
      owner: null, // No owner specified for generated keys
      exportable: false,
      uploadedToCtm: false,
      ctmKeyName: null,
      userId,
    });

    try {
      const result = await this.seqrngClientService.generateAlphanumericKey(
        generateKeyDto,
        ctmConfig,
        seqrngConfig,
      );

      // Update key record with success
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully generated alphanumeric key for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to generate alphanumeric key for user ${userId}`, error);
      throw new BadRequestException(`Failed to generate alphanumeric key: ${error.message}`);
    }
  }

  /**
   * Upload key to CTM using user's configuration
   */
  async uploadKeyToCtm(userId: string, uploadKeyDto: UploadKeyDto) {
    this.logger.log(`Uploading key '${uploadKeyDto.key_name}' to CTM for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    // Get user information to determine owner
    const user = await this.usersService.findOne(userId);
    const owner = this.determineOwner(user);

    // Create key record
    const keyRecord = this.keyRepository.create({
      name: uploadKeyDto.key_name,
      type: KeyType.HEX_KEY, // Assuming uploaded keys are hex by default
      algorithm: uploadKeyDto.algorithm ? this.mapAlgorithmToEnum(uploadKeyDto.algorithm) : null,
      numBytes: uploadKeyDto.num_bytes || 32,
      status: KeyStatus.GENERATED,
      owner: owner,
      exportable: uploadKeyDto.exportable || false,
      uploadedToCtm: false,
      ctmKeyName: uploadKeyDto.key_name,
      userId,
    });

    try {
      // Add owner to the DTO for the CTM request
      const uploadKeyDtoWithOwner = {
        ...uploadKeyDto,
        owner: owner,
      };

      const result = await this.seqrngClientService.uploadKeyToCtm(
        uploadKeyDtoWithOwner,
        ctmConfig,
        seqrngConfig,
      );

      // Update key record with success
      keyRecord.status = KeyStatus.UPLOADED_TO_CTM;
      keyRecord.uploadedToCtm = true;
      keyRecord.entropyReport = JSON.stringify(result.data?.entropy_report || {});
      keyRecord.ctmKeyId = result.data?.ctm_key_id || null;
      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully uploaded key '${uploadKeyDto.key_name}' to CTM for user ${userId}`);
      return result;
    } catch (error) {
      // Update key record with error
      keyRecord.status = KeyStatus.FAILED;
      keyRecord.errorMessage = error.message;
      await this.keyRepository.save(keyRecord);

      this.logger.error(`Failed to upload key to CTM for user ${userId}`, error);
      throw new BadRequestException(`Failed to upload key to CTM: ${error.message}`);
    }
  }

  /**
   * Upload multiple keys to CTM using user's configuration
   */
  async uploadBatchKeysToCtm(userId: string, uploadBatchKeysDto: UploadBatchKeysDto) {
    this.logger.log(`Uploading batch keys with prefix '${uploadBatchKeysDto.key_name_prefix}' to CTM for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    // Get user information to determine owner
    const user = await this.usersService.findOne(userId);
    const owner = this.determineOwner(user);

    try {
      // Add owner to the DTO for the CTM request
      const uploadBatchKeysDtoWithOwner = {
        ...uploadBatchKeysDto,
        owner: owner,
      };

      const result = await this.seqrngClientService.uploadBatchKeysToCtm(
        uploadBatchKeysDtoWithOwner,
        ctmConfig,
        seqrngConfig,
      );

      this.logger.log(`Successfully uploaded batch keys to CTM for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to upload batch keys to CTM for user ${userId}`, error);
      throw new BadRequestException(`Failed to upload batch keys to CTM: ${error.message}`);
    }
  }

  /**
   * Check if key exists in CTM using user's configuration
   */
  async checkKeyExists(userId: string, keyName: string) {
    this.logger.log(`Checking if key '${keyName}' exists in CTM for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    try {
      const result = await this.seqrngClientService.checkKeyExists(keyName, ctmConfig, seqrngConfig);

      this.logger.log(`Successfully checked key existence for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to check key existence for user ${userId}`, error);
      throw new BadRequestException(`Failed to check key existence: ${error.message}`);
    }
  }

  /**
   * Get CTM authentication token using user's configuration
   */
  async getCtmToken(userId: string) {
    this.logger.log(`Getting CTM token for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    try {
      const result = await this.seqrngClientService.getCtmToken(ctmConfig, seqrngConfig);

      this.logger.log(`Successfully obtained CTM token for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to get CTM token for user ${userId}`, error);
      throw new BadRequestException(`Failed to get CTM token: ${error.message}`);
    }
  }

  /**
   * Determine owner based on user information
   */
  private determineOwner(user: any): string {
    // If user has a company, use it as owner
    if (user.company && user.company.trim()) {
      return user.company.trim().toLowerCase().replace(/\s+/g, '_');
    }

    // Otherwise, use firstName_lastName format
    return `${user.firstName}_${user.lastName}`.toLowerCase();
  }

  /**
   * Get user's CTM configuration and validate it exists
   */
  private async getUserCtmConfig(userId: string): Promise<CtmConfig> {
    const ctmConfig = await this.usersService.getUserCtmConfig(userId);

    if (!ctmConfig) {
      throw new ForbiddenException(
        'User does not have CTM configuration. Please contact your administrator to set up CTM access.',
      );
    }

    return ctmConfig;
  }

  /**
   * Get user's SeqRNG configuration and validate it exists
   */
  private async getUserSeqrngConfig(userId: string): Promise<SeqrngConfig | undefined> {
    const seqrngConfig = await this.usersService.getUserSeqrngConfig(userId);

    // SeqRNG config is optional - if not provided, use global config from Flask API
    return seqrngConfig || undefined;
  }

  /**
   * Get user's keys with pagination and filters
   */
  async getUserKeys(userId: string, getKeysDto: GetKeysDto): Promise<KeyListResponseDto> {
    this.logger.log(`Getting keys for user ${userId}`);

    const {
      page = 1,
      limit = 10,
      type,
      algorithm,
      status,
      uploadedToCtm,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = getKeysDto;

    const queryBuilder = this.keyRepository
      .createQueryBuilder('key')
      .where('key.userId = :userId', { userId });

    // Apply filters
    if (type) {
      queryBuilder.andWhere('key.type = :type', { type });
    }

    if (algorithm) {
      queryBuilder.andWhere('key.algorithm = :algorithm', { algorithm });
    }

    if (status) {
      queryBuilder.andWhere('key.status = :status', { status });
    }

    if (uploadedToCtm !== undefined) {
      queryBuilder.andWhere('key.uploadedToCtm = :uploadedToCtm', { uploadedToCtm });
    }

    if (search) {
      queryBuilder.andWhere(
        '(key.name ILIKE :search OR key.ctmKeyName ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    const validSortFields = ['createdAt', 'updatedAt', 'name', 'type', 'status'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    queryBuilder.orderBy(`key.${sortField}`, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [keys, total] = await queryBuilder.getManyAndCount();

    // Calculate statistics
    const allKeysQuery = this.keyRepository
      .createQueryBuilder('key')
      .where('key.userId = :userId', { userId });

    const [allKeys] = await allKeysQuery.getManyAndCount();
    const successful = await allKeysQuery
      .andWhere('key.status != :failedStatus', { failedStatus: KeyStatus.FAILED })
      .getCount();
    const failed = total - successful;
    const uploadedToCtmCount = await allKeysQuery
      .andWhere('key.uploadedToCtm = :uploaded', { uploaded: true })
      .getCount();

    // Transform to DTOs
    const keyDtos: KeyResponseDto[] = keys.map(key => ({
      id: key.id,
      name: key.name,
      type: key.type,
      algorithm: key.algorithm,
      numBytes: key.numBytes,
      status: key.status,
      owner: key.owner,
      exportable: key.exportable,
      uploadedToCtm: key.uploadedToCtm,
      ctmKeyName: key.ctmKeyName,
      ctmKeyId: key.ctmKeyId,
      entropyReport: key.entropyReport,
      errorMessage: key.errorMessage,
      createdAt: key.createdAt,
      updatedAt: key.updatedAt,
      isSuccessful: key.isSuccessful,
      displayName: key.displayName,
      version: key.version,
    }));

    return {
      keys: keyDtos,
      total,
      successful,
      failed,
      uploadedToCtm: uploadedToCtmCount,
    };
  }

  /**
   * Get a specific key by ID for a user
   */
  async getUserKey(userId: string, keyId: string): Promise<KeyResponseDto> {
    this.logger.log(`Getting key ${keyId} for user ${userId}`);

    const key = await this.keyRepository.findOne({
      where: { id: keyId, userId },
    });

    if (!key) {
      throw new BadRequestException('Key not found');
    }

    return {
      id: key.id,
      name: key.name,
      type: key.type,
      algorithm: key.algorithm,
      numBytes: key.numBytes,
      status: key.status,
      owner: key.owner,
      exportable: key.exportable,
      uploadedToCtm: key.uploadedToCtm,
      ctmKeyName: key.ctmKeyName,
      ctmKeyId: key.ctmKeyId,
      entropyReport: key.entropyReport,
      errorMessage: key.errorMessage,
      createdAt: key.createdAt,
      updatedAt: key.updatedAt,
      isSuccessful: key.isSuccessful,
      displayName: key.displayName,
      version: key.version,
    };
  }

  /**
   * Delete a key by ID for a user
   */
  async deleteUserKey(userId: string, keyId: string): Promise<void> {
    this.logger.log(`Deleting key ${keyId} for user ${userId}`);

    const result = await this.keyRepository.delete({
      id: keyId,
      userId,
    });

    if (result.affected === 0) {
      throw new BadRequestException('Key not found');
    }

    this.logger.log(`Successfully deleted key ${keyId} for user ${userId}`);
  }

  /**
   * Create a new version of an existing key with quantum material
   */
  async createKeyVersion(userId: string, keyId: string, archivePrevious: boolean = false) {
    this.logger.log(`Creating new version for key ${keyId} for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    // Get user information to determine owner
    const user = await this.usersService.findOne(userId);
    const owner = this.determineOwner(user);

    try {
      // First, find the base key in our database to get version info
      const baseKey = await this.keyRepository.findOne({
        where: { ctmKeyId: keyId, userId },
      });

      if (!baseKey) {
        throw new BadRequestException(`Key with CTM ID '${keyId}' not found in database`);
      }

      // Determine the base key ID and next version number
      const baseKeyId = baseKey.baseKeyId || baseKey.id; // If it's already a version, use its baseKeyId
      const currentMaxVersion = await this.keyRepository
        .createQueryBuilder('key')
        .select('MAX(key.version)', 'maxVersion')
        .where('(key.baseKeyId = :baseKeyId OR key.id = :baseKeyId)', { baseKeyId })
        .getRawOne();

      const nextVersion = (currentMaxVersion?.maxVersion || 0) + 1;

      // Get key information from CTM to validate it exists and get details
      const keyInfoResult = await this.seqrngClientService.getKeyInfo(
        keyId,
        ctmConfig,
        seqrngConfig,
      );

      if (keyInfoResult.status !== 'success') {
        throw new BadRequestException(`Key with ID '${keyId}' not found or inaccessible in CTM`);
      }

      const keyInfo = keyInfoResult.data;
      const keyName = keyInfo.name || 'unknown';
      const algorithm = keyInfo.algorithm || 'aes';

      // Create new version with quantum material
      const result = await this.seqrngClientService.createKeyVersion(
        keyId,
        archivePrevious,
        ctmConfig,
        seqrngConfig,
      );

      if (result.status !== 'success') {
        throw new BadRequestException(`Failed to create new version: ${result.message}`);
      }

      // Create a new key record for the version
      const keyRecord = this.keyRepository.create({
        name: `${keyName}_v${nextVersion}`,
        type: KeyType.HEX_KEY, // Assuming versioned keys are hex by default
        algorithm: this.mapAlgorithmToEnum(algorithm),
        numBytes: result.data.material_length || 32,
        status: KeyStatus.UPLOADED_TO_CTM,
        owner: owner,
        exportable: false, // Versioned keys are typically not exportable
        uploadedToCtm: true,
        ctmKeyName: keyName,
        ctmKeyId: result.data.version_id, // Store the version ID as the CTM key ID
        entropyReport: JSON.stringify(result.data.entropy_report || {}),
        baseKeyId: baseKeyId, // Reference to the base key
        version: nextVersion, // Set the version number
        userId,
      });

      await this.keyRepository.save(keyRecord);

      this.logger.log(`Successfully created new version for key ${keyId} for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to create key version for user ${userId}`, error);
      throw new BadRequestException(`Failed to create key version: ${error.message}`);
    }
  }

  /**
   * Get information about a specific key from CTM
   */
  async getKeyInfo(userId: string, keyId: string) {
    this.logger.log(`Getting key info for ${keyId} for user ${userId}`);

    const ctmConfig = await this.getUserCtmConfig(userId);
    const seqrngConfig = await this.getUserSeqrngConfig(userId);

    try {
      const result = await this.seqrngClientService.getKeyInfo(
        keyId,
        ctmConfig,
        seqrngConfig,
      );

      this.logger.log(`Successfully retrieved key info for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to get key info for user ${userId}`, error);
      throw new BadRequestException(`Failed to get key information: ${error.message}`);
    }
  }

  /**
   * Get all versions of a key by base key ID
   */
  async getKeyVersions(userId: string, baseKeyId: string) {
    this.logger.log(`Getting all versions for base key ${baseKeyId} for user ${userId}`);

    try {
      // Find all versions of the key (including the base key itself)
      const versions = await this.keyRepository.find({
        where: [
          { id: baseKeyId, userId }, // The base key itself
          { baseKeyId: baseKeyId, userId }, // All versions of the base key
        ],
        order: { version: 'ASC' },
      });

      if (versions.length === 0) {
        throw new BadRequestException(`No key found with base ID '${baseKeyId}'`);
      }

      // Transform to DTOs
      const versionDtos = versions.map(key => ({
        id: key.id,
        name: key.name,
        type: key.type,
        algorithm: key.algorithm,
        numBytes: key.numBytes,
        status: key.status,
        owner: key.owner,
        exportable: key.exportable,
        uploadedToCtm: key.uploadedToCtm,
        ctmKeyName: key.ctmKeyName,
        ctmKeyId: key.ctmKeyId,
        baseKeyId: key.baseKeyId,
        version: key.version,
        createdAt: key.createdAt,
        updatedAt: key.updatedAt,
      }));

      this.logger.log(`Found ${versions.length} versions for base key ${baseKeyId}`);
      return {
        baseKeyId,
        totalVersions: versions.length,
        versions: versionDtos,
      };
    } catch (error) {
      this.logger.error(`Failed to get key versions for user ${userId}`, error);
      throw new BadRequestException(`Failed to get key versions: ${error.message}`);
    }
  }
}
