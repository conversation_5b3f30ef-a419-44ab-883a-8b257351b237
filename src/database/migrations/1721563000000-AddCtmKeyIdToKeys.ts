import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddCtmKeyIdToKeys1721563000000 implements MigrationInterface {
  name = 'AddCtmKeyIdToKeys1721563000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add ctmKeyId column to keys table (only if it doesn't exist)
    const hasColumn = await queryRunner.hasColumn('keys', 'ctmKeyId');
    if (!hasColumn) {
      await queryRunner.addColumn(
        'keys',
        new TableColumn({
          name: 'ctmKeyId',
          type: 'text',
          isNullable: true,
        }),
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove ctmKeyId column from keys table (only if it exists)
    const hasColumn = await queryRunner.hasColumn('keys', 'ctmKeyId');
    if (hasColumn) {
      await queryRunner.dropColumn('keys', 'ctmKeyId');
    }
  }
}
