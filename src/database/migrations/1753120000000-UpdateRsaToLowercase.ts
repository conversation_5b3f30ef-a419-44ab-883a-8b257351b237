import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateRsaToLowercase1753120000000 implements MigrationInterface {
  name = 'UpdateRsaToLowercase1753120000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, change the column to text temporarily
    await queryRunner.query(`ALTER TABLE "keys" ALTER COLUMN "algorithm" TYPE text`);
    
    // Update existing RSA data to lowercase
    await queryRunner.query(`
      UPDATE "keys" 
      SET "algorithm" = 'rsa' 
      WHERE "algorithm" = 'RSA'
    `);

    // Now update the enum to include rsa instead of RSA
    await queryRunner.query(`DROP TYPE "public"."keys_algorithm_enum"`);
    await queryRunner.query(`CREATE TYPE "public"."keys_algorithm_enum" AS ENUM('aes', 'aria', 'rsa', 'hmac-sha1', 'hmac-sha256', 'hmac-sha384', 'hmac-sha512')`);
    await queryRunner.query(`ALTER TABLE "keys" ALTER COLUMN "algorithm" TYPE "public"."keys_algorithm_enum" USING "algorithm"::"text"::"public"."keys_algorithm_enum"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Change column to text temporarily
    await queryRunner.query(`ALTER TABLE "keys" ALTER COLUMN "algorithm" TYPE text`);
    
    // Revert data changes
    await queryRunner.query(`
      UPDATE "keys" 
      SET "algorithm" = 'RSA' 
      WHERE "algorithm" = 'rsa'
    `);

    // Revert enum changes
    await queryRunner.query(`DROP TYPE "public"."keys_algorithm_enum"`);
    await queryRunner.query(`CREATE TYPE "public"."keys_algorithm_enum" AS ENUM('aes', 'aria', 'RSA', 'hmac-sha1', 'hmac-sha256', 'hmac-sha384', 'hmac-sha512')`);
    await queryRunner.query(`ALTER TABLE "keys" ALTER COLUMN "algorithm" TYPE "public"."keys_algorithm_enum" USING "algorithm"::"text"::"public"."keys_algorithm_enum"`);
  }
}
