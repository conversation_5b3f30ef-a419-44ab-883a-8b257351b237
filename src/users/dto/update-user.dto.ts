import { IsEmail, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ption<PERSON>, IsE<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../entities/user.entity';

export class UpdateUserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email del usuario',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    example: 'Juan',
    description: 'Nombre del usuario',
    minLength: 2,
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName?: string;

  @ApiProperty({
    example: 'Pérez',
    description: 'Apellido del usuario',
    minLength: 2,
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  lastName?: string;

  @ApiProperty({
    example: 'Acme Corporation',
    description: 'Empresa del usuario',
    maxLength: 200,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  company?: string;

  @ApiProperty({
    example: UserRole.USER,
    description: 'Rol del usuario',
    enum: UserRole,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  // CTM Configuration fields (optional)
  @ApiProperty({
    example: 'https://ctm.example.com:443',
    description: 'Dirección IP/URL del CipherTrust Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  ctmIpAddress?: string;

  @ApiProperty({
    example: 'ctm_admin',
    description: 'Usuario del CipherTrust Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  ctmUsername?: string;

  @ApiProperty({
    example: 'ctm_password123',
    description: 'Contraseña del CipherTrust Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(128)
  ctmPassword?: string;

  @ApiProperty({
    example: 'root',
    description: 'Dominio del CipherTrust Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  ctmDomain?: string;

  // SeqRNG Configuration fields (optional)
  @ApiProperty({
    example: 'https://seqrng.example.com:1982',
    description: 'Dirección IP/URL del SeqRNG',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  seqrngIpAddress?: string;

  @ApiProperty({
    example: '1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL',
    description: 'Token de API del SeqRNG',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  seqrngApiToken?: string;
}
